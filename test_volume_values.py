#!/usr/bin/env python3
"""
Test script to check if volume voxel values match original image pixel values
"""

import os
import sys
import numpy as np
from PIL import Image

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dataprocess.volume import Volume

def test_volume_pixel_values():
    """
    Test if the volume voxel values match the original image pixel values
    """
    print("Testing volume voxel values vs original image pixel values...")
    
    # Test parameters from convert_seg.py
    input_dir = "F:/dev/CT/3d-seg/datasets/MitoEM-R/seg-cp/mito-val-v2"
    output_dir = "F:/dev/CT/3d-seg/datasets/MitoEM-R/seg/val"
    
    # Check if directories exist
    if not os.path.exists(input_dir):
        print(f"Input directory does not exist: {input_dir}")
        return
    
    if not os.path.exists(output_dir):
        print(f"Output directory does not exist: {output_dir}")
        return
    
    # Test the first volume
    volume_file = os.path.join(output_dir, "em_0_0_0.zst")
    if not os.path.exists(volume_file):
        print(f"Volume file does not exist: {volume_file}")
        return
    
    print(f"Loading volume: {volume_file}")
    volume = Volume(volume_file)
    volume.load()
    
    print(f"Volume shape: {volume.volume.shape}")
    print(f"Volume dtype: {volume.volume.dtype}")
    print(f"Volume min: {np.min(volume.volume)}")
    print(f"Volume max: {np.max(volume.volume)}")
    
    # Load corresponding original images
    print("\nLoading original images...")
    original_images = []
    for i in range(100):  # volume_depth = 100
        img_index = i + 400  # From convert_seg.py line 60
        img_path = os.path.join(input_dir, f"seg{img_index:04d}.tif")
        
        if not os.path.exists(img_path):
            print(f"Image does not exist: {img_path}")
            continue
            
        img = Image.open(img_path).convert("L")
        img_array = np.array(img)
        original_images.append(img_array)
        
        if i == 0:  # Print info for first image
            print(f"First image shape: {img_array.shape}")
            print(f"First image dtype: {img_array.dtype}")
            print(f"First image min: {np.min(img_array)}")
            print(f"First image max: {np.max(img_array)}")
    
    if not original_images:
        print("No original images found!")
        return
    
    # Stack original images to create expected volume
    original_volume = np.stack(original_images, axis=0)
    
    # Extract the region that should correspond to the first volume (0,0,0)
    # From convert_seg.py: volume_height=1024, volume_width=1024
    # First volume corresponds to [0:1024, 0:1024] region
    expected_subvolume = original_volume[:, 0:1024, 0:1024]
    
    print(f"\nExpected subvolume shape: {expected_subvolume.shape}")
    print(f"Expected subvolume dtype: {expected_subvolume.dtype}")
    print(f"Expected subvolume min: {np.min(expected_subvolume)}")
    print(f"Expected subvolume max: {np.max(expected_subvolume)}")
    
    # Compare values
    print(f"\nActual volume shape: {volume.volume.shape}")
    print(f"Expected shape: {expected_subvolume.shape}")
    
    if volume.volume.shape != expected_subvolume.shape:
        print("ERROR: Shapes don't match!")
        return
    
    # Check if values are identical
    values_match = np.array_equal(volume.volume, expected_subvolume)
    print(f"Values match exactly: {values_match}")
    
    if not values_match:
        print("ERROR: Values don't match!")
        
        # Check data types
        print(f"Volume dtype: {volume.volume.dtype}")
        print(f"Expected dtype: {expected_subvolume.dtype}")
        
        # Check if it's a data type conversion issue
        if volume.volume.dtype != expected_subvolume.dtype:
            print("Data type mismatch detected!")
            
            # Try converting expected to volume's dtype
            expected_converted = expected_subvolume.astype(volume.volume.dtype)
            values_match_after_conversion = np.array_equal(volume.volume, expected_converted)
            print(f"Values match after dtype conversion: {values_match_after_conversion}")
        
        # Show some sample differences
        diff = volume.volume.astype(np.float64) - expected_subvolume.astype(np.float64)
        print(f"Max absolute difference: {np.max(np.abs(diff))}")
        print(f"Mean absolute difference: {np.mean(np.abs(diff))}")
        
        # Show some sample values
        print("\nSample comparison (first 5x5 of first slice):")
        print("Volume values:")
        print(volume.volume[0, :5, :5])
        print("Expected values:")
        print(expected_subvolume[0, :5, :5])
        print("Difference:")
        print(diff[0, :5, :5])
    else:
        print("SUCCESS: Volume values match original image pixel values!")

if __name__ == "__main__":
    test_volume_pixel_values()
